"""
Data models for the AI Contact Research Agent
Pydantic models for request/response validation and data structures
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class ResearchStatus(str, Enum):
    """Status of contact research"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class ResearchDepth(str, Enum):
    """Research depth settings"""
    QUICK = "quick"
    STANDARD = "standard"
    DEEP = "deep"

class ColumnMapping(BaseModel):
    """Column mapping configuration for CSV processing"""
    file_id: str
    company_name_column: str
    website_column: Optional[str] = None
    email_column: Optional[str] = None
    phone_column: Optional[str] = None
    additional_columns: List[str] = []

    model_config = {
        "json_schema_extra": {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-426614174000",
                "company_name_column": "Company Name",
                "website_column": "Website",
                "email_column": "Email",
                "phone_column": "Phone",
                "additional_columns": ["Industry", "Location", "Size"]
            }
        }
    }

class ContactRecord(BaseModel):
    """Individual contact record with research data"""
    id: Optional[str] = None
    company_name: str
    website: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    additional_data: Dict[str, Any] = {}

    # Research metadata
    research_status: ResearchStatus = ResearchStatus.PENDING
    last_researched: Optional[datetime] = None
    research_notes: Optional[str] = None
    data_sources: List[str] = []
    confidence_score: Optional[float] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "company_name": "Apple Inc.",
                "website": "https://www.apple.com",
                "email": "<EMAIL>",
                "phone": "******-996-1010",
                "research_status": "completed",
                "confidence_score": 0.95
            }
        }
    }

class ResearchCommand(BaseModel):
    """Command for starting research process"""
    file_id: str
    command_text: str
    research_depth: ResearchDepth = ResearchDepth.STANDARD
    target_companies: Optional[List[str]] = None
    skip_existing: bool = True
    max_companies: Optional[int] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-426614174000",
                "command_text": "Research all companies",
                "research_depth": "standard",
                "skip_existing": True,
                "max_companies": 100
            }
        }
    }

class APIConfig(BaseModel):
    """API configuration settings"""
    openrouter_api_key: Optional[str] = None
    openrouter_model: str = Field(default="openai/gpt-3.5-turbo")
    gemini_api_key: Optional[str] = None
    gemini_model: str = Field(default="gemini-pro")
    rate_limit_per_minute: int = Field(default=30, ge=1, le=60)
    delay_between_requests: float = Field(default=2.0, ge=0.5, le=10.0)
    max_retries: int = Field(default=3, ge=1, le=10)
    timeout_seconds: int = Field(default=30, ge=10, le=120)
    research_depth: ResearchDepth = ResearchDepth.STANDARD
    max_pages_per_website: int = Field(default=3, ge=1, le=10)

    @field_validator('openrouter_api_key', 'gemini_api_key')
    @classmethod
    def validate_api_keys(cls, v):
        if v and len(v.strip()) < 10:
            raise ValueError('API key appears to be too short')
        return v

    model_config = {
        "json_schema_extra": {
            "example": {
                "openrouter_api_key": "sk-or-v1-...",
                "openrouter_model": "openai/gpt-3.5-turbo",
                "gemini_api_key": "AIza...",
                "gemini_model": "gemini-pro",
                "rate_limit_per_minute": 30,
                "delay_between_requests": 2.0,
                "research_depth": "standard"
            }
        }
    }

class ResearchProgress(BaseModel):
    """Real-time research progress information"""
    session_id: str
    file_id: str
    total_companies: int
    processed_companies: int
    successful_researches: int
    failed_researches: int
    skipped_companies: int
    current_company: Optional[str] = None
    current_stage: Optional[str] = None
    estimated_time_remaining: Optional[int] = None  # seconds
    start_time: datetime
    last_update: datetime

    @property
    def completion_percentage(self) -> float:
        if self.total_companies == 0:
            return 0.0
        return round((self.processed_companies / self.total_companies) * 100, 1)

    model_config = {
        "json_schema_extra": {
            "example": {
                "session_id": "research-123",
                "file_id": "file-456",
                "total_companies": 100,
                "processed_companies": 45,
                "successful_researches": 40,
                "failed_researches": 3,
                "skipped_companies": 2,
                "current_company": "Apple Inc.",
                "current_stage": "extracting_emails"
            }
        }
    }

class ChatMessage(BaseModel):
    """Chat message for natural language interface"""
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    message_type: str = "user"  # user, assistant, system
    session_id: Optional[str] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "message": "Research all companies with missing emails",
                "message_type": "user"
            }
        }
    }

class ChatResponse(BaseModel):
    """Response from chat interface"""
    response: str
    action_taken: Optional[str] = None
    research_started: bool = False
    session_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

    model_config = {
        "json_schema_extra": {
            "example": {
                "response": "I'll start researching companies with missing emails. Found 25 companies to research.",
                "action_taken": "start_research",
                "research_started": True,
                "session_id": "research-789"
            }
        }
    }

class ExportRequest(BaseModel):
    """Request for exporting data"""
    file_id: str
    format: str = "csv"  # csv, excel, json
    include_metadata: bool = False
    filter_complete_only: bool = False
    custom_filename: Optional[str] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-426614174000",
                "format": "csv",
                "include_metadata": True,
                "filter_complete_only": False
            }
        }
    }

class DataQualityReport(BaseModel):
    """Data quality analysis report"""
    file_id: str
    total_records: int
    complete_records: int
    incomplete_records: int
    completion_percentage: float

    # Field-specific completion rates
    website_completion: float
    email_completion: float
    phone_completion: float

    # Quality scores
    overall_quality_score: float
    data_freshness_score: float
    confidence_score: float

    # Recommendations
    recommendations: List[str]

    model_config = {
        "json_schema_extra": {
            "example": {
                "file_id": "file-123",
                "total_records": 100,
                "complete_records": 85,
                "completion_percentage": 85.0,
                "website_completion": 90.0,
                "email_completion": 80.0,
                "phone_completion": 75.0,
                "overall_quality_score": 8.5,
                "recommendations": [
                    "Consider deeper research for companies with missing phone numbers",
                    "Verify email addresses for higher confidence"
                ]
            }
        }
    }

class ErrorLog(BaseModel):
    """Error logging for research process"""
    session_id: str
    company_name: str
    error_type: str
    error_message: str
    timestamp: datetime = Field(default_factory=datetime.now)
    retry_count: int = 0
    resolved: bool = False

    model_config = {
        "json_schema_extra": {
            "example": {
                "session_id": "research-123",
                "company_name": "Example Corp",
                "error_type": "website_not_found",
                "error_message": "No website found in search results",
                "retry_count": 2
            }
        }
    }
