"""
Enhanced tools for the AI Contact Research Agent
Includes Crawl4AI integration and advanced extraction capabilities
"""

import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse

# LangChain imports
from langchain.tools import Tool
from langchain_community.tools import DuckDuckGoSearchRun

# Web scraping and data processing
import requests
from bs4 import BeautifulSoup
import phonenumbers
from email_validator import validate_email, EmailNotValidError

# Crawl4AI integration
try:
    from crawl4ai import WebCrawler
    from crawl4ai.extraction_strategy import LLMExtractionStrategy
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    WebCrawler = None
    LLMExtractionStrategy = None

# Local imports
from config import Settings, ScrapingConfig

logger = logging.getLogger(__name__)

class EnhancedContactTools:
    """Enhanced tools for contact research with Crawl4AI integration"""
    
    def __init__(self, rate_limit: int = 30):
        self.settings = Settings()
        self.rate_limit = rate_limit
        self.last_request_time = 0
        
        # Initialize search tool
        self.search_tool = DuckDuckGoSearchRun()
        
        # Initialize Crawl4AI if available
        self.crawler = None
        if CRAWL4AI_AVAILABLE:
            self.crawler = WebCrawler(verbose=True)
            
    def get_tools(self) -> List[Tool]:
        """Get all available tools for the agent"""
        tools = []
        
        # Enhanced search tool
        search_tool = Tool(
            name="enhanced_web_search",
            description="Search the web for company information, websites, and contact details",
            func=self._enhanced_search
        )
        tools.append(search_tool)
        
        # Advanced website scraping tool
        scrape_tool = Tool(
            name="advanced_scrape_website",
            description="Scrape a website using advanced techniques to extract contact information",
            func=self._advanced_scrape_website
        )
        tools.append(scrape_tool)
        
        # Contact page finder tool
        contact_finder_tool = Tool(
            name="find_contact_pages",
            description="Find contact and about pages on a website",
            func=self._find_contact_pages
        )
        tools.append(contact_finder_tool)
        
        # Email extraction tool
        email_tool = Tool(
            name="extract_emails_advanced",
            description="Extract and validate email addresses from text content",
            func=self._extract_emails_advanced
        )
        tools.append(email_tool)
        
        # Phone extraction tool
        phone_tool = Tool(
            name="extract_phones_advanced",
            description="Extract and format phone numbers from text content",
            func=self._extract_phones_advanced
        )
        tools.append(phone_tool)
        
        # Company validation tool
        validation_tool = Tool(
            name="validate_company_info",
            description="Validate and score the quality of found company information",
            func=self._validate_company_info
        )
        tools.append(validation_tool)
        
        return tools
    
    def _enhanced_search(self, query: str) -> str:
        """Enhanced search with better result parsing"""
        try:
            # Add search modifiers for better results
            enhanced_query = f"{query} contact information official website"
            results = self.search_tool.run(enhanced_query)
            
            # Parse and structure results
            structured_results = self._parse_search_results(results)
            return str(structured_results)
            
        except Exception as e:
            logger.error(f"Enhanced search error: {str(e)}")
            return f"Search failed: {str(e)}"
    
    def _advanced_scrape_website(self, url: str) -> str:
        """Advanced website scraping using Crawl4AI if available"""
        try:
            if CRAWL4AI_AVAILABLE and self.crawler:
                return self._crawl4ai_scrape(url)
            else:
                return self._fallback_scrape(url)
                
        except Exception as e:
            logger.error(f"Advanced scraping error for {url}: {str(e)}")
            return f"Scraping failed: {str(e)}"
    
    def _crawl4ai_scrape(self, url: str) -> str:
        """Use Crawl4AI for advanced scraping"""
        try:
            # Define extraction strategy for contact information
            extraction_strategy = LLMExtractionStrategy(
                provider="openai",
                api_token=self.settings.openrouter_api_key,
                instruction="""
                Extract contact information from this webpage including:
                - Email addresses (especially info@, contact@, sales@, support@)
                - Phone numbers (all formats)
                - Physical addresses
                - Contact forms or contact page links
                - Social media links
                
                Return the information in a structured format.
                """
            )
            
            # Crawl the website
            result = self.crawler.run(
                url=url,
                extraction_strategy=extraction_strategy,
                bypass_cache=True
            )
            
            if result.success:
                return f"Content: {result.cleaned_html}\nExtracted: {result.extracted_content}"
            else:
                return f"Crawl4AI failed: {result.error_message}"
                
        except Exception as e:
            logger.error(f"Crawl4AI error: {str(e)}")
            return self._fallback_scrape(url)
    
    def _fallback_scrape(self, url: str) -> str:
        """Fallback scraping method using requests and BeautifulSoup"""
        try:
            headers = ScrapingConfig.HEADERS.copy()
            headers['User-Agent'] = ScrapingConfig.USER_AGENTS[0]
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract text content
            text_content = soup.get_text()
            
            # Extract structured data
            structured_data = {
                'title': soup.title.string if soup.title else '',
                'meta_description': '',
                'text_content': text_content[:5000],  # Limit content
                'links': [a.get('href') for a in soup.find_all('a', href=True)][:20]
            }
            
            # Get meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                structured_data['meta_description'] = meta_desc.get('content', '')
            
            return str(structured_data)
            
        except Exception as e:
            return f"Fallback scraping failed: {str(e)}"
    
    def _find_contact_pages(self, base_url: str) -> str:
        """Find contact and about pages on a website"""
        try:
            contact_pages = []
            
            for path in ScrapingConfig.CONTACT_PATHS:
                try:
                    contact_url = urljoin(base_url, path)
                    headers = ScrapingConfig.HEADERS.copy()
                    
                    response = requests.head(contact_url, headers=headers, timeout=10)
                    if response.status_code == 200:
                        contact_pages.append(contact_url)
                        
                except Exception:
                    continue
            
            return f"Found contact pages: {contact_pages}"
            
        except Exception as e:
            return f"Contact page search failed: {str(e)}"
    
    def _extract_emails_advanced(self, text: str) -> str:
        """Advanced email extraction with validation and scoring"""
        try:
            emails = []
            
            # Use multiple regex patterns
            for pattern in self.settings.email_patterns:
                found_emails = re.findall(pattern, text, re.IGNORECASE)
                emails.extend(found_emails)
            
            # Validate and score emails
            validated_emails = []
            for email in emails:
                try:
                    validated = validate_email(email)
                    email_address = validated.email
                    
                    # Score email based on prefix
                    score = self._score_email(email_address)
                    validated_emails.append({
                        'email': email_address,
                        'score': score,
                        'type': self._classify_email(email_address)
                    })
                    
                except EmailNotValidError:
                    continue
            
            # Sort by score (highest first)
            validated_emails.sort(key=lambda x: x['score'], reverse=True)
            
            return str(validated_emails)
            
        except Exception as e:
            return f"Email extraction failed: {str(e)}"
    
    def _extract_phones_advanced(self, text: str) -> str:
        """Advanced phone extraction with international formatting"""
        try:
            phones = []
            
            # Use multiple regex patterns
            for pattern in self.settings.phone_patterns:
                found_phones = re.findall(pattern, text)
                phones.extend(found_phones)
            
            # Validate and format phones
            validated_phones = []
            for phone in phones:
                try:
                    # Try parsing with different country codes
                    for country in ['US', 'CA', 'GB', 'AU']:
                        try:
                            parsed = phonenumbers.parse(phone, country)
                            if phonenumbers.is_valid_number(parsed):
                                formatted = phonenumbers.format_number(
                                    parsed, 
                                    phonenumbers.PhoneNumberFormat.INTERNATIONAL
                                )
                                validated_phones.append({
                                    'phone': formatted,
                                    'country': country,
                                    'type': phonenumbers.number_type(parsed).name
                                })
                                break
                        except Exception:
                            continue
                            
                except Exception:
                    # Keep original if parsing fails but looks like a phone
                    cleaned = re.sub(r'[^\d+()-.\s]', '', phone)
                    if len(re.sub(r'[^\d]', '', cleaned)) >= 10:
                        validated_phones.append({
                            'phone': cleaned.strip(),
                            'country': 'unknown',
                            'type': 'unknown'
                        })
            
            # Remove duplicates
            unique_phones = []
            seen = set()
            for phone_data in validated_phones:
                phone_key = re.sub(r'[^\d]', '', phone_data['phone'])
                if phone_key not in seen:
                    seen.add(phone_key)
                    unique_phones.append(phone_data)
            
            return str(unique_phones)
            
        except Exception as e:
            return f"Phone extraction failed: {str(e)}"
    
    def _validate_company_info(self, company_data: str) -> str:
        """Validate and score company information quality"""
        try:
            # Parse company data (assuming it's a string representation of dict)
            # This would be enhanced based on actual data structure
            
            quality_score = 0.0
            quality_factors = []
            
            # Check for website
            if 'website' in company_data.lower():
                quality_score += 0.3
                quality_factors.append("Website found")
            
            # Check for email
            if '@' in company_data:
                quality_score += 0.3
                quality_factors.append("Email found")
            
            # Check for phone
            if any(char.isdigit() for char in company_data):
                quality_score += 0.2
                quality_factors.append("Phone found")
            
            # Check for multiple sources
            if len(quality_factors) > 1:
                quality_score += 0.2
                quality_factors.append("Multiple data sources")
            
            validation_result = {
                'quality_score': min(quality_score, 1.0),
                'quality_factors': quality_factors,
                'completeness': len(quality_factors) / 3.0  # Assuming 3 main factors
            }
            
            return str(validation_result)
            
        except Exception as e:
            return f"Validation failed: {str(e)}"
    
    def _parse_search_results(self, results: str) -> Dict[str, Any]:
        """Parse and structure search results"""
        try:
            # Extract URLs from results
            urls = re.findall(
                r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', 
                results
            )
            
            # Filter out social media and directory sites
            filtered_urls = []
            for url in urls:
                if not any(skip in url.lower() for skip in ScrapingConfig.SKIP_DOMAINS):
                    filtered_urls.append(url)
            
            return {
                'raw_results': results[:1000],  # Truncate for brevity
                'extracted_urls': filtered_urls[:5],  # Top 5 URLs
                'url_count': len(filtered_urls)
            }
            
        except Exception as e:
            return {'error': str(e), 'raw_results': results[:500]}
    
    def _score_email(self, email: str) -> float:
        """Score email based on business relevance"""
        email_lower = email.lower()
        
        # High-value prefixes
        if any(prefix in email_lower for prefix in ['info@', 'contact@', 'sales@']):
            return 1.0
        
        # Medium-value prefixes
        if any(prefix in email_lower for prefix in ['support@', 'admin@', 'hello@']):
            return 0.8
        
        # General business email
        if any(prefix in email_lower for prefix in ['office@', 'team@', 'mail@']):
            return 0.6
        
        # Personal or low-value
        return 0.3
    
    def _classify_email(self, email: str) -> str:
        """Classify email type"""
        email_lower = email.lower()
        
        if any(prefix in email_lower for prefix in ['info@', 'contact@']):
            return 'general_contact'
        elif any(prefix in email_lower for prefix in ['sales@', 'business@']):
            return 'sales'
        elif any(prefix in email_lower for prefix in ['support@', 'help@']):
            return 'support'
        elif any(prefix in email_lower for prefix in ['admin@', 'office@']):
            return 'administrative'
        else:
            return 'other'
